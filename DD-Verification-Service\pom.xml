<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>net.plus</groupId>
    <artifactId>dd-verification-service</artifactId>
    <version>0.53.0</version>
    <name>dd-verification-service</name>
    <packaging>pom</packaging>
    <description>DirectDebit verification application running in its own PHP8 docker container</description>

    <parent>
        <groupId>net.plus.php</groupId>
        <artifactId>plusnet-php-ant-pom</artifactId>
        <version>3.3.1-SNAPSHOT</version>
    </parent>

    <properties>
        <docker.repo>docker-registry.env.plus.net</docker.repo>
        <docker.artifactId>dd-verification-service</docker.artifactId>
        <docker.image.prefix>plusnet</docker.image.prefix>
        <docker.baseimage>plusnet-base/pn-linux-php-8.2</docker.baseimage>
        <dockerfile-maven-plugin.version>1.4.13</dockerfile-maven-plugin.version>
        <skipPackageInstall>true</skipPackageInstall>
        <skipExecution>true</skipExecution>
        <skipTestExecution>true</skipTestExecution>
        <skipDeployment>true</skipDeployment>
    </properties>
    <build>
        <directory>target</directory>
        <sourceDirectory>Libraries</sourceDirectory>
        <testSourceDirectory>Test</testSourceDirectory>
        <plugins>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <tarLongFileMode>gnu</tarLongFileMode>
                    <descriptors>
                        <descriptor>distribution.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>${dockerfile-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>build-image</id>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <configuration>
                            <noCache>true</noCache>
                        </configuration>
                    </execution>
                    <execution>
                        <id>push-image</id>
                        <goals>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                    <repository>${docker.repo}/${docker.image.prefix}/${docker.artifactId}</repository>
                    <tag>${project.version}</tag>
                    <buildArgs>
                        <BASE_IMAGE>${docker.repo}/${docker.baseimage}</BASE_IMAGE>
                        <SERVICE_NAME>dd-verification-service</SERVICE_NAME>
                        <BASELINE_NAME>dd-verification-service-baseline</BASELINE_NAME>
                        <BRANCH_NAME>master</BRANCH_NAME>
                    </buildArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <scm>
        <connection>scm:ssh://**************************:7999/Billing/dd-verification-service.git</connection>
        <developerConnection>scm:ssh://**************************:7999/Billing/dd-verification-service.git
        </developerConnection>
        <url>https://bitbucket.int.plus.net/projects/Billing/repos/dd-verification-service.git</url>
    </scm>
    <ciManagement>
        <system>Jenkins</system>
        <url>https://jenkins.env.plus.net/job/Build/job/DirectDebitVerification-Service</url>
    </ciManagement>
</project>
